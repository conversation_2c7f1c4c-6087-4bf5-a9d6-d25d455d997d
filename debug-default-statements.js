// Debug script to check what defaultStatements contains
// Run this in the browser console or Node.js

import { defaultStatements } from "better-auth/plugins/organization/access";

console.log("Default Statements from Better Auth:", defaultStatements);
console.log("Keys:", Object.keys(defaultStatements));

// Check if 'ac' resource is included
if (defaultStatements.ac) {
  console.log("AC resource permissions:", defaultStatements.ac);
} else {
  console.log("❌ AC resource not found in defaultStatements");
}

// Show all resources and their permissions
Object.entries(defaultStatements).forEach(([resource, actions]) => {
  console.log(`${resource}:`, actions);
});
