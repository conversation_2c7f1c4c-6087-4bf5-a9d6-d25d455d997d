// Debug script to check what permissions are included in Better Auth
// Run this in Node.js to see what's in defaultStatements and adminAc

const { defaultStatements, adminAc, memberAc } = require("better-auth/plugins/organization/access");

console.log("=== DEFAULT STATEMENTS ===");
console.log(JSON.stringify(defaultStatements, null, 2));

console.log("\n=== ADMIN AC STATEMENTS ===");
console.log(JSON.stringify(adminAc.statements, null, 2));

console.log("\n=== MEMBER AC STATEMENTS ===");
console.log(JSON.stringify(memberAc.statements, null, 2));

// Check if 'ac' resource is included
if (defaultStatements.ac) {
  console.log("\n✅ AC resource found in defaultStatements:", defaultStatements.ac);
} else {
  console.log("\n❌ AC resource NOT found in defaultStatements");
}

if (adminAc.statements.ac) {
  console.log("✅ AC resource found in adminAc.statements:", adminAc.statements.ac);
} else {
  console.log("❌ AC resource NOT found in adminAc.statements");
}
