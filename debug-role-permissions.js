// Debug script to check user permissions and role creation
// Run this in your browser console on the organization settings page

console.log("=== Debugging Role Creation Permissions ===");

// 1. Check current user session
console.log("1. Current user session:");
try {
  // This should be available in your React app
  const userQuery = window.__REACT_QUERY_STATE__?.queries?.find(q => 
    q.queryKey?.includes('user.getUser')
  );
  console.log("User data:", userQuery?.state?.data);
} catch (e) {
  console.log("Could not access user data from React Query cache");
}

// 2. Check organization data
console.log("\n2. Organization data:");
try {
  const orgQuery = window.__REACT_QUERY_STATE__?.queries?.find(q => 
    q.queryKey?.includes('organizations.getBySlug')
  );
  console.log("Organization data:", orgQuery?.state?.data);
} catch (e) {
  console.log("Could not access organization data from React Query cache");
}

// 3. Check member role
console.log("\n3. Member role:");
try {
  const roleQuery = window.__REACT_QUERY_STATE__?.queries?.find(q => 
    q.queryKey?.includes('organizations.getMemberRole')
  );
  console.log("Member role data:", roleQuery?.state?.data);
} catch (e) {
  console.log("Could not access member role data from React Query cache");
}

// 4. Check what permissions are being sent
console.log("\n4. To debug the exact permissions being sent:");
console.log("Open Network tab, try to create a role, and check the request payload");
console.log("Look for the 'permission' field in the request body");

// 5. Manual check - you can run this in console
console.log("\n5. Manual checks you can run:");
console.log(`
// Check if you're admin:
fetch('/api/trpc/organizations.getMemberRole', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    json: { id: 'YOUR_USER_ID', orgId: 'YOUR_ORG_ID' }
  })
}).then(r => r.json()).then(console.log);

// List existing roles:
fetch('/api/trpc/organizations.listRoles', {
  method: 'POST', 
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    json: { orgId: 'YOUR_ORG_ID' }
  })
}).then(r => r.json()).then(console.log);
`);

console.log("=== End Debug Info ===");
