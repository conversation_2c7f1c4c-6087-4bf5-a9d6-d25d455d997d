#!/usr/bin/env tsx

/**
 * <PERSON><PERSON>t to check user's role in organizations and debug permission issues
 * 
 * Usage:
 * npx tsx scripts/check-member-role.ts [email] [orgSlug]
 * 
 * Examples:
 * npx tsx scripts/check-member-role.ts <EMAIL> my-org
 * npx tsx scripts/check-member-role.ts <EMAIL>  # Check all orgs for user
 * npx tsx scripts/check-member-role.ts  # Interactive mode
 */

import { db } from "../src/server/db";
import readline from "readline";

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
});

function question(prompt: string): Promise<string> {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
}

async function checkMemberRole(email?: string, orgSlug?: string) {
  try {
    console.log("🔍 Checking member roles...\n");

    // If no email provided, ask for it
    if (!email) {
      email = await question("Enter user email: ");
    }

    // Find the user
    const user = await db.user.findUnique({
      where: { email },
      select: {
        id: true,
        name: true,
        email: true,
        members: {
          where: { deletedAt: null },
          select: {
            id: true,
            role: true,
            createdAt: true,
            organization: {
              select: {
                id: true,
                name: true,
                slug: true,
              },
            },
          },
        },
      },
    });

    if (!user) {
      console.log(`❌ User not found: ${email}`);
      return;
    }

    console.log(`✅ User found: ${user.name} (${user.email})`);
    console.log(`   User ID: ${user.id}\n`);

    if (user.members.length === 0) {
      console.log("❌ User is not a member of any organizations");
      return;
    }

    console.log(`📋 Organization memberships (${user.members.length}):\n`);

    for (const member of user.members) {
      const org = member.organization;
      const isTargetOrg = !orgSlug || org.slug === orgSlug;
      const marker = isTargetOrg ? "🎯" : "  ";
      
      console.log(`${marker} Organization: ${org.name}`);
      console.log(`   Slug: ${org.slug}`);
      console.log(`   ID: ${org.id}`);
      console.log(`   Role: ${member.role}`);
      console.log(`   Member ID: ${member.id}`);
      console.log(`   Joined: ${member.createdAt.toISOString()}`);
      
      if (isTargetOrg) {
        console.log(`   🔑 This is ${member.role === 'admin' ? 'an ADMIN' : 'NOT an admin'} role`);
        
        // Check if there are any Better Auth organization roles
        const betterAuthRoles = await db.organizationRole.findMany({
          where: { organizationId: org.id },
          select: {
            id: true,
            role: true,
            permission: true,
            createdAt: true,
          },
        });
        
        if (betterAuthRoles.length > 0) {
          console.log(`   📝 Better Auth dynamic roles in this org (${betterAuthRoles.length}):`);
          for (const role of betterAuthRoles) {
            console.log(`      - ${role.role} (created: ${role.createdAt.toISOString()})`);
            console.log(`        Permissions: ${JSON.stringify(role.permission, null, 8)}`);
          }
        } else {
          console.log(`   📝 No Better Auth dynamic roles found in this org`);
        }
      }
      
      console.log("");
    }

    // If specific org requested, show more details
    if (orgSlug) {
      const targetMember = user.members.find(m => m.organization.slug === orgSlug);
      if (targetMember) {
        console.log("🔧 Troubleshooting suggestions:");
        
        if (targetMember.role !== 'admin') {
          console.log("❌ Issue: User role is not 'admin'");
          console.log(`   Current role: ${targetMember.role}`);
          console.log(`   To fix: Update the member role to 'admin' in the database`);
          console.log(`   SQL: UPDATE members SET role = 'admin' WHERE id = '${targetMember.id}';`);
        } else {
          console.log("✅ User has admin role in database");
          console.log("❓ The issue might be with Better Auth dynamic access control");
          console.log("   Check if the admin role definition includes required Better Auth permissions");
        }
      } else {
        console.log(`❌ User is not a member of organization: ${orgSlug}`);
      }
    }

  } catch (error) {
    console.error("❌ Error checking member role:", error);
  } finally {
    await db.$disconnect();
    rl.close();
  }
}

// Parse command line arguments
const args = process.argv.slice(2);
const email = args[0];
const orgSlug = args[1];

// Run the script
checkMemberRole(email, orgSlug).catch(console.error);
