"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { api } from "@/trpc/react";
import { useState } from "react";

export function DatabaseRoleCheck() {
  const [result, setResult] = useState<string>("");
  
  // Get all organizations for the user
  const organizations = api.organizations.getAll.useQuery({ take: 500 });
  
  const checkDatabaseRole = async () => {
    if (!organizations.data || organizations.data.length === 0) {
      setResult("❌ No organizations found for user");
      return;
    }

    let resultText = "=== DATABASE ROLE CHECK ===\n\n";
    
    for (const org of organizations.data) {
      resultText += `Organization: ${org.name} (${org.id})\n`;
      resultText += `Slug: ${org.slug}\n`;
      
      // Find the member record for this org
      try {
        const members = await api.organizations.getMembers.useQuery({ id: org.id });
        // This won't work directly in component, let's use a different approach
        resultText += `Members count: ${org._count?.members || 0}\n`;
      } catch (error) {
        resultText += `Error getting members: ${error}\n`;
      }
      
      resultText += "\n";
    }
    
    setResult(resultText);
  };

  return (
    <Card className="w-full max-w-4xl">
      <CardHeader>
        <CardTitle>Database Role Check</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <p className="text-sm text-gray-600 mb-2">
            Organizations found: {organizations.data?.length || 0}
          </p>
          
          {organizations.data?.map((org) => (
            <div key={org.id} className="p-3 border rounded mb-2">
              <div><strong>Name:</strong> {org.name}</div>
              <div><strong>ID:</strong> {org.id}</div>
              <div><strong>Slug:</strong> {org.slug}</div>
              <div><strong>Members:</strong> {org._count?.members || 0}</div>
            </div>
          ))}
        </div>
        
        <Button onClick={checkDatabaseRole}>Check Database Roles</Button>
        
        {result && (
          <div>
            <h3 className="font-semibold">Results:</h3>
            <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto max-h-96 whitespace-pre-wrap">
              {result}
            </pre>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
