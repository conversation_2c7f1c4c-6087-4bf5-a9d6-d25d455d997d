"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { authClient } from "@/libs/auth-client";
import { api } from "@/trpc/react";
import { useState } from "react";

export function RoleCreationSimpleTest() {
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<string>("");

  const createRoleMutation = api.organization.createRole.useMutation({
    onSuccess: (data) => {
      setResult(`✅ Success: Role created with ID ${data.id}`);
      setIsLoading(false);
    },
    onError: (error) => {
      setResult(`❌ Error: ${error.message}`);
      setIsLoading(false);
    },
  });

  const testMinimalRole = async () => {
    setIsLoading(true);
    setResult("Creating minimal role...");
    
    try {
      // Test with minimal permissions first
      await createRoleMutation.mutateAsync({
        name: `test-minimal-${Date.now()}`,
        permissions: {
          project: ["read"], // Just one simple permission
        },
      });
    } catch (error) {
      console.error("Role creation failed:", error);
    }
  };

  const testACPermissions = async () => {
    setIsLoading(true);
    setResult("Testing AC permissions...");
    
    try {
      // Check if user has AC permissions
      const hasACPermission = await authClient.organization.hasPermission({
        permissions: {
          ac: ["create"], // Check if user can create AC resources
        },
      });
      
      setResult(`AC Permission Check: ${hasACPermission ? "✅ Has permission" : "❌ No permission"}`);
      setIsLoading(false);
    } catch (error) {
      setResult(`❌ AC Permission Error: ${error}`);
      setIsLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle>Simple Role Creation Test</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex gap-2">
          <Button 
            onClick={testACPermissions}
            disabled={isLoading}
            variant="outline"
          >
            Test AC Permissions
          </Button>
          <Button 
            onClick={testMinimalRole}
            disabled={isLoading}
          >
            Test Minimal Role Creation
          </Button>
        </div>
        
        {result && (
          <div className="p-4 bg-gray-50 rounded-md">
            <pre className="text-sm whitespace-pre-wrap">{result}</pre>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
