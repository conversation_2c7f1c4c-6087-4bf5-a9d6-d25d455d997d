"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useOrganizationSlug } from "@/hooks/use-organization-slug";
import { api } from "@/trpc/react";
import { useOrganizationBySlug } from "@/queries/organization.queries";
import { useState } from "react";

export function RoleCreationTest() {
  const [roleName, setRoleName] = useState("");
  const [testResults, setTestResults] = useState<any[]>([]);
  
  const slug = useOrganizationSlug();
  const organization = useOrganizationBySlug(slug);

  const createRole = api.organizations.createRole.useMutation({
    onSuccess: (data) => {
      setTestResults(prev => [...prev, {
        type: "success",
        timestamp: new Date().toISOString(),
        data,
        message: "Role created successfully"
      }]);
    },
    onError: (error) => {
      setTestResults(prev => [...prev, {
        type: "error",
        timestamp: new Date().toISOString(),
        error: error.message,
        fullError: error,
        message: "Role creation failed"
      }]);
    },
  });

  const testCases = [
    {
      name: "Minimal permissions",
      permissions: { project: ["read"] }
    },
    {
      name: "Basic project permissions",
      permissions: { project: ["create", "read", "update"] }
    },
    {
      name: "Member-like permissions",
      permissions: { project: ["create", "read", "update", "submit"] }
    },
    {
      name: "Manager-like permissions", 
      permissions: { 
        project: ["create", "read", "update", "submit"],
        approval: ["review"]
      }
    },
    {
      name: "Role management permissions",
      permissions: { role: ["create", "read", "update", "delete"] }
    },
    {
      name: "Empty permissions (should fail)",
      permissions: {}
    }
  ];

  const runTest = async (testCase: typeof testCases[0]) => {
    if (!organization.data?.id) {
      setTestResults(prev => [...prev, {
        type: "error",
        timestamp: new Date().toISOString(),
        message: "No organization ID available"
      }]);
      return;
    }

    const testRoleName = `test-${testCase.name.toLowerCase().replace(/\s+/g, '-')}-${Date.now()}`;
    
    setTestResults(prev => [...prev, {
      type: "info",
      timestamp: new Date().toISOString(),
      message: `Testing: ${testCase.name}`,
      permissions: testCase.permissions,
      roleName: testRoleName
    }]);

    try {
      await createRole.mutateAsync({
        orgId: organization.data.id,
        role: testRoleName,
        permission: testCase.permissions,
      });
    } catch (error) {
      // Error is already handled by onError
    }
  };

  const runCustomTest = async () => {
    if (!roleName.trim() || !organization.data?.id) return;

    const customPermissions = { project: ["read"] };
    
    try {
      await createRole.mutateAsync({
        orgId: organization.data.id,
        role: roleName,
        permission: customPermissions,
      });
    } catch (error) {
      // Error is already handled by onError
    }
  };

  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <div className="space-y-4 p-4">
      <Card>
        <CardHeader>
          <CardTitle>Role Creation Test Suite</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-2">
            {testCases.map((testCase, index) => (
              <Button
                key={index}
                onClick={() => runTest(testCase)}
                disabled={createRole.isPending}
                variant={testCase.name.includes("should fail") ? "destructive" : "default"}
                size="sm"
              >
                {testCase.name}
              </Button>
            ))}
          </div>

          <div className="flex gap-2">
            <div className="flex-1">
              <Label htmlFor="roleName">Custom Role Name</Label>
              <Input
                id="roleName"
                value={roleName}
                onChange={(e) => setRoleName(e.target.value)}
                placeholder="Enter role name"
              />
            </div>
            <Button 
              onClick={runCustomTest}
              disabled={createRole.isPending || !roleName.trim()}
              className="mt-6"
            >
              Test Custom Role
            </Button>
          </div>

          <div className="flex gap-2">
            <Button onClick={clearResults} variant="outline">
              Clear Results
            </Button>
          </div>

          {testResults.length > 0 && (
            <div className="mt-4">
              <h3 className="font-semibold mb-2">Test Results:</h3>
              <div className="space-y-2 max-h-96 overflow-auto">
                {testResults.map((result, index) => (
                  <div
                    key={index}
                    className={`p-2 rounded text-sm ${
                      result.type === "success" 
                        ? "bg-green-100 text-green-800" 
                        : result.type === "error"
                        ? "bg-red-100 text-red-800"
                        : "bg-blue-100 text-blue-800"
                    }`}
                  >
                    <div className="font-medium">
                      {result.type.toUpperCase()}: {result.message}
                    </div>
                    <div className="text-xs opacity-75">
                      {new Date(result.timestamp).toLocaleTimeString()}
                    </div>
                    {result.permissions && (
                      <details className="mt-1">
                        <summary className="cursor-pointer">Permissions</summary>
                        <pre className="text-xs mt-1 bg-white bg-opacity-50 p-1 rounded">
                          {JSON.stringify(result.permissions, null, 2)}
                        </pre>
                      </details>
                    )}
                    {result.error && (
                      <div className="mt-1 text-xs">
                        Error: {result.error}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
