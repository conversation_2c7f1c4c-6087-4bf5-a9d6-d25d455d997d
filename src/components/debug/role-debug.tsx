"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON>Content, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { useOrganizationSlug } from "@/hooks/use-organization-slug";
import { usePermissions } from "@/hooks/use-permissions";
import { api } from "@/trpc/react";
import { useOrganizationBySlug, useOrganizationMemberRole } from "@/queries/organization.queries";
import { useUser } from "@/queries/user.queries";
import { useState } from "react";

export function RoleDebugComponent() {
  const [debugInfo, setDebugInfo] = useState<any>(null);
  const [testResult, setTestResult] = useState<any>(null);
  
  const user = useUser();
  const slug = useOrganizationSlug();
  const organization = useOrganizationBySlug(slug);
  const memberRole = useOrganizationMemberRole();
  const permissions = usePermissions();

  const createRole = api.organizations.createRole.useMutation({
    onSuccess: (data) => {
      setTestResult({ success: true, data });
    },
    onError: (error) => {
      setTestResult({ success: false, error: error.message, fullError: error });
    },
  });

  const gatherDebugInfo = () => {
    const info = {
      user: user.data,
      organization: organization.data,
      memberRole,
      permissions,
      slug,
      timestamp: new Date().toISOString(),
    };
    setDebugInfo(info);
    console.log("Debug Info:", info);
  };

  const testMinimalRoleCreation = async () => {
    if (!organization.data?.id) {
      setTestResult({ success: false, error: "No organization ID" });
      return;
    }

    // Test with minimal permissions
    const minimalPermissions = {
      project: ["read"], // Just read permission
    };

    try {
      await createRole.mutateAsync({
        orgId: organization.data.id,
        role: `test-role-${Date.now()}`,
        permission: minimalPermissions,
      });
    } catch (error) {
      console.error("Role creation failed:", error);
    }
  };

  const testEmptyRoleCreation = async () => {
    if (!organization.data?.id) {
      setTestResult({ success: false, error: "No organization ID" });
      return;
    }

    // Test with empty permissions (should fail with "No valid permissions supplied")
    try {
      await createRole.mutateAsync({
        orgId: organization.data.id,
        role: `empty-role-${Date.now()}`,
        permission: {},
      });
    } catch (error) {
      console.error("Empty role creation failed (expected):", error);
    }
  };

  return (
    <div className="space-y-4 p-4">
      <Card>
        <CardHeader>
          <CardTitle>Role Creation Debug Tool</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <Button onClick={gatherDebugInfo}>Gather Debug Info</Button>
            <Button onClick={testMinimalRoleCreation} disabled={createRole.isPending}>
              Test Minimal Role Creation
            </Button>
            <Button onClick={testEmptyRoleCreation} disabled={createRole.isPending}>
              Test Empty Role (Should Fail)
            </Button>
          </div>

          {debugInfo && (
            <div className="mt-4">
              <h3 className="font-semibold">Debug Information:</h3>
              <pre className="bg-gray-100 p-2 rounded text-xs overflow-auto max-h-96">
                {JSON.stringify(debugInfo, null, 2)}
              </pre>
            </div>
          )}

          {testResult && (
            <div className="mt-4">
              <h3 className="font-semibold">Test Result:</h3>
              <div className={`p-2 rounded ${testResult.success ? 'bg-green-100' : 'bg-red-100'}`}>
                {testResult.success ? (
                  <div>
                    <p className="text-green-800">✅ Success!</p>
                    <pre className="text-xs mt-2">{JSON.stringify(testResult.data, null, 2)}</pre>
                  </div>
                ) : (
                  <div>
                    <p className="text-red-800">❌ Error: {testResult.error}</p>
                    <details className="mt-2">
                      <summary className="cursor-pointer text-sm">Full Error Details</summary>
                      <pre className="text-xs mt-2 bg-red-50 p-2 rounded">
                        {JSON.stringify(testResult.fullError, null, 2)}
                      </pre>
                    </details>
                  </div>
                )}
              </div>
            </div>
          )}

          <div className="mt-4 text-sm text-gray-600">
            <h4 className="font-semibold">Quick Status:</h4>
            <ul className="list-disc list-inside space-y-1">
              <li>User loaded: {user.data ? '✅' : '❌'}</li>
              <li>Organization loaded: {organization.data ? '✅' : '❌'}</li>
              <li>Member role: {memberRole || 'Not loaded'}</li>
              <li>Is admin: {memberRole === 'admin' ? '✅' : '❌'}</li>
              <li>Can create roles: {permissions.isAdmin ? '✅' : '❌'}</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
