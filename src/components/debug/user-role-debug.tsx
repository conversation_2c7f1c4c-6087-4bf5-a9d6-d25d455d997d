"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useOrganizationSlug } from "@/hooks/use-organization-slug";
import { authClient } from "@/libs/auth-client";
import { useOrganizationBySlug, useOrganizationMemberRole } from "@/queries/organization.queries";
import { useUser } from "@/queries/user.queries";
import { api } from "@/trpc/react";
import { useState } from "react";

export function UserRoleDebug() {
  const [debugInfo, setDebugInfo] = useState<any>(null);
  const user = useUser();
  const slug = useOrganizationSlug();
  const organization = useOrganizationBySlug(slug);
  const memberRole = useOrganizationMemberRole();

  const checkRole = async () => {
    try {
      // Get current session
      const session = await authClient.getSession();
      
      // Get organization member info
      const orgData = organization.data;
      const userData = user.data;
      
      setDebugInfo({
        session: session,
        user: userData,
        organization: orgData,
        memberRole: memberRole,
        slug: slug,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      setDebugInfo({
        error: error instanceof Error ? error.message : "Unknown error",
        timestamp: new Date().toISOString(),
      });
    }
  };

  return (
    <Card className="w-full max-w-4xl">
      <CardHeader>
        <CardTitle>User Role Debug Information</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <Button onClick={checkRole}>Check Current Role & Session</Button>
        
        {debugInfo && (
          <div className="space-y-4">
            <div>
              <h3 className="font-semibold">Debug Info:</h3>
              <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto max-h-96">
                {JSON.stringify(debugInfo, null, 2)}
              </pre>
            </div>
            
            {debugInfo.memberRole && (
              <div className="p-4 bg-blue-50 rounded">
                <strong>Current Role: {debugInfo.memberRole}</strong>
              </div>
            )}
            
            {debugInfo.error && (
              <div className="p-4 bg-red-50 rounded text-red-700">
                <strong>Error: {debugInfo.error}</strong>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
