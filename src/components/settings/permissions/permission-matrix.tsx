"use client";

/**
 * TODO: This component needs to be updated to work with the simplified Better Auth permissions system.
 * The old complex permission matrix system has been replaced with Better Auth's standard approach.
 * For now, this component is disabled until it can be properly refactored.
 */

import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { Separator } from "@/components/ui/separator";
import { admin, getStatements, manager, member } from "@/libs/auth-permissions";
import {
  useOrgRoles,
  useUpdatePermissionOverridesMutation,
} from "@/queries/organization.queries";
import { Roles } from "@/types/organization.types";
import {
  IconChevronDown,
  IconChevronRight,
  IconFile,
  IconKey,
  IconLocation,
  IconSettings,
  IconShield,
  IconThumbUp,
  IconUsers,
} from "@tabler/icons-react";
import { useState } from "react";
import { toast } from "sonner";

interface Props {
  organizationId?: string;
}

const roleLabels = {
  [Roles.ADMIN]: "Administrator",
  [Roles.MANAGER]: "Manager",
  [Roles.MEMBER]: "Member",
};

const resourceIcons = {
  project: IconFile,
  slide: IconFile,
  approval: IconThumbUp,
  member: IconUsers,
  organization: IconSettings,
  location: IconLocation,
  role: IconShield,
  ac: IconKey,
};

const resourceLabels = {
  project: "Projects",
  slide: "Slides",
  approval: "Approvals",
  member: "Members",
  organization: "Organization",
  location: "Locations",
  role: "Roles",
  ac: "Access Control",
};

const actionLabels = {
  create: "Create",
  read: "Read",
  update: "Update",
  delete: "Delete",
  submit: "Submit",
  approve: "Approve",
  publish: "Publish",
  assign_locations: "Assign Locations",
  manage_slides: "Manage Slides",
  upload: "Upload",
  reorder: "Reorder",
  mark_important: "Mark Important",
  view: "View",
  review: "Review",
  resubmit: "Resubmit",
  invite: "Invite",
  update_role: "Update Role",
  remove: "Remove",
  billing: "Billing",
  view_reports: "View Reports",
  assign: "Assign",
  manage: "Manage",
};

export function PermissionMatrix({ organizationId }: Props) {
  const [collapsedResources, setCollapsedResources] = useState<Set<string>>(
    new Set(),
  );

  const updatePermissionsMutation = useUpdatePermissionOverridesMutation();
  const rolesQuery = useOrgRoles(organizationId || "");
  const rolesData: Array<{
    roleName?: string;
    role?: string;
    permission?: Record<string, string[]>;
  }> = Array.isArray(rolesQuery.data)
    ? (rolesQuery.data as any)
    : ((rolesQuery.data as any)?.roles ?? []);

  const getRolePermission = (role: string): Record<string, string[]> => {
    const found = rolesData.find((r) => (r.roleName ?? r.role) === role);
    return (found?.permission ?? {}) as Record<string, string[]>;
  };

  const toggleResourceCollapsed = (resource: string) => {
    const newCollapsed = new Set(collapsedResources);
    if (newCollapsed.has(resource)) {
      newCollapsed.delete(resource);
    } else {
      newCollapsed.add(resource);
    }
    setCollapsedResources(newCollapsed);
  };

  const handlePermissionChange = async (
    role: string,
    resource: string,
    action: string,
    checked: boolean,
  ) => {
    if (!organizationId) return;

    try {
      if (checked) {
        // Grant permission
        await updatePermissionsMutation.mutateAsync({
          orgId: organizationId,
          role: role as any,
          permissions: {
            [resource]: [action],
          },
        });
      } else {
        // TODO: In v1, we only support additive permissions
        // Removing permissions would require a more complex implementation
        toast.info(
          "Removing permissions is not yet supported. You can only grant additional permissions.",
        );
      }
    } catch (error) {
      console.error("Failed to update permission:", error);
    }
  };

  const isPermissionGranted = (
    role: string,
    resource: string,
    action: string,
  ) => {
    const permission = getRolePermission(role);
    return (permission[resource] || []).includes(action);
  };

  const isPermissionBase = (role: string, resource: string, action: string) => {
    // Get base permissions from Better Auth roles
    let baseRole;
    switch (role) {
      case Roles.ADMIN:
        baseRole = admin;
        break;
      case Roles.MANAGER:
        baseRole = manager;
        break;
      case Roles.MEMBER:
        baseRole = member;
        break;
      default:
        return false;
    }

    // Check if the role has this permission in its base definition
    const rolePermissions = baseRole.statements;
    return rolePermissions[resource]?.includes(action) ?? false;
  };

  const isPermissionOverride = (
    role: string,
    resource: string,
    action: string,
  ) => {
    return (
      isPermissionGranted(role, resource, action) &&
      !isPermissionBase(role, resource, action)
    );
  };

  const roles = [Roles.ADMIN, Roles.MANAGER, Roles.MEMBER];
  const statements = getStatements();
  const resources = Object.keys(statements);

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="grid grid-cols-12 items-center gap-4 border-b pb-3 text-sm font-medium text-gray-900">
        <div className="col-span-3">Resource / Action</div>
        {roles.map((role) => (
          <div key={role} className="col-span-3 text-center">
            <div className="flex items-center justify-center gap-2">
              <IconShield className="h-4 w-4" />
              {roleLabels[role]}
            </div>
          </div>
        ))}
      </div>

      {/* Resources */}
      <div className="space-y-2">
        {resources.map((resource) => {
          const Icon = resourceIcons[resource] || IconFile;
          const actions = statements[resource];
          const isCollapsed = collapsedResources.has(resource);

          return (
            <Collapsible
              key={resource}
              open={!isCollapsed}
              onOpenChange={() => toggleResourceCollapsed(resource)}
            >
              <CollapsibleTrigger asChild>
                <Button
                  variant="ghost"
                  className="h-auto w-full justify-start px-0 py-2 font-medium hover:bg-gray-50"
                >
                  <div className="flex items-center gap-2">
                    {isCollapsed ? (
                      <IconChevronRight className="h-4 w-4" />
                    ) : (
                      <IconChevronDown className="h-4 w-4" />
                    )}
                    <Icon className="h-4 w-4" />
                    <span>{resourceLabels[resource]}</span>
                    <Badge variant="secondary" className="ml-2">
                      {actions.length} actions
                    </Badge>
                  </div>
                </Button>
              </CollapsibleTrigger>

              <CollapsibleContent className="space-y-1 pl-6">
                {actions.map((action) => (
                  <div
                    key={action}
                    className="grid grid-cols-12 items-center gap-4 border-b border-gray-100 py-2 text-sm last:border-b-0"
                  >
                    {/* Action name */}
                    <div className="col-span-3 text-gray-700">
                      {actionLabels[action as keyof typeof actionLabels] ||
                        action}
                    </div>

                    {/* Role permissions */}
                    {roles.map((role) => {
                      const isGranted = isPermissionGranted(
                        role,
                        resource,
                        action,
                      );
                      const isBase = isPermissionBase(role, resource, action);
                      const isOverride = isPermissionOverride(
                        role,
                        resource,
                        action,
                      );

                      return (
                        <div
                          key={role}
                          className="col-span-3 flex justify-center"
                        >
                          <div className="flex items-center gap-2">
                            <Checkbox
                              checked={isGranted}
                              disabled={
                                isBase || updatePermissionsMutation.isPending
                              }
                              onCheckedChange={(checked) =>
                                handlePermissionChange(
                                  role,
                                  resource,
                                  action,
                                  checked as boolean,
                                )
                              }
                            />
                            {isBase && (
                              <Badge
                                variant="outline"
                                className="border-blue-200 bg-blue-50 text-xs text-blue-700"
                              >
                                Base
                              </Badge>
                            )}
                            {isOverride && (
                              <Badge
                                variant="outline"
                                className="border-green-200 bg-green-50 text-xs text-green-700"
                              >
                                Override
                              </Badge>
                            )}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                ))}
              </CollapsibleContent>
            </Collapsible>
          );
        })}
      </div>

      {/* Legend */}
      <Separator />
      <div className="text-sm text-gray-600">
        <h4 className="mb-2 font-medium">Legend:</h4>
        <div className="flex flex-wrap gap-4">
          <div className="flex items-center gap-2">
            <Badge
              variant="outline"
              className="border-blue-200 bg-blue-50 text-xs text-blue-700"
            >
              Base
            </Badge>
            <span>Default role permission</span>
          </div>
          <div className="flex items-center gap-2">
            <Badge
              variant="outline"
              className="border-green-200 bg-green-50 text-xs text-green-700"
            >
              Override
            </Badge>
            <span>Additional granted permission</span>
          </div>
        </div>
      </div>
    </div>
  );
}
