"use client";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { DeleteDialog } from "@/components/ui/delete-dialog";
import { useDialog } from "@/hooks/use-dialog";
import {
  useDeleteOrgRoleMutation,
  useOrgRoles,
} from "@/queries/organization.queries";
import { PermissionMap } from "./permission-selector";
import { RoleCreateDialog } from "./role-create-dialog";
import { RoleEditDialog } from "./role-edit-dialog";

import { Roles } from "@/types/organization.types";
import { IconEdit, IconPlus, IconTrash } from "@tabler/icons-react";
import { useState } from "react";

interface Props {
  orgId?: string;
}

const isBuiltIn = (name: string) =>
  name === Roles.ADMIN || name === Roles.MANAGER || name === Roles.MEMBER;

export function PermissionRoleManagement({ orgId }: Props) {
  const rolesQuery = useOrgRoles(orgId || "");

  const deleteRole = useDeleteOrgRoleMutation();

  const [openDeleteDialog, deleteDialogHandlers] = useDialog();
  const [roleToDelete, setRoleToDelete] = useState<string | null>(null);

  const roles: Array<{
    roleName?: string;
    role?: string;
    permission?: PermissionMap;
  }> = rolesQuery.data ?? [];

  const [createDialogOpen, createDialogHandlers] = useDialog();
  const [editDialogOpen, editDialogHandlers] = useDialog();
  const [editing, setEditing] = useState<null | {
    name: string;
    permission: PermissionMap;
  }>(null);

  const openCreate = () => {
    createDialogHandlers.open();
  };

  const openEdit = (name: string, permission: PermissionMap = {}) => {
    setEditing({ name, permission });
    editDialogHandlers.open();
  };

  const onRequestDelete = (roleName: string) => {
    if (isBuiltIn(roleName)) return;
    setRoleToDelete(roleName);
    deleteDialogHandlers.open();
  };

  const onConfirmDelete = async () => {
    if (!orgId || !roleToDelete) return;
    await deleteRole.mutateAsync({ orgId, roleName: roleToDelete });
    setRoleToDelete(null);
    deleteDialogHandlers.close();
  };

  return (
    <Card>
      <div className="flex items-center justify-between border-b border-gray-200 bg-sidebar px-6 py-4">
        <p className="text-base leading-6 font-medium">Roles</p>
        <Button
          size="sm"
          leftIcon={<IconPlus className="h-4 w-4" />}
          onClick={openCreate}
          disabled={!orgId}
        >
          Create Role
        </Button>
      </div>

      <div className="p-6">
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          {(roles || []).map((r: any) => {
            const roleName = r.roleName ?? r.role ?? "";
            const permission: PermissionMap = r.permission ?? {};
            const isCore = isBuiltIn(roleName);
            return (
              <Card key={roleName} className="p-4">
                <div className="flex items-start justify-between gap-4">
                  <div>
                    <div className="flex items-center gap-2">
                      <span className="font-medium">{roleName}</span>
                      {isCore && (
                        <Badge
                          variant="outline"
                          className="border-blue-200 bg-blue-50 text-xs text-blue-700"
                        >
                          Built-in
                        </Badge>
                      )}
                    </div>
                    <div className="mt-2 flex flex-wrap gap-2 text-sm">
                      {Object.entries(permission).length === 0 && (
                        <span className="text-gray-400">
                          No additive permissions
                        </span>
                      )}
                      {Object.entries(permission).flatMap(([res, acts]) => {
                        // Ensure acts is an array before calling map
                        const actionsArray = Array.isArray(acts) ? acts : [];
                        return actionsArray.map((a) => (
                          <Badge
                            key={`${roleName}-${res}-${a}`}
                            variant="secondary"
                          >
                            {res}:{a}
                          </Badge>
                        ));
                      })}
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => openEdit(roleName, permission)}
                      leftIcon={<IconEdit className="h-4 w-4" />}
                    >
                      Edit
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      disabled={isCore}
                      onClick={() => onRequestDelete(roleName)}
                      leftIcon={<IconTrash className="h-4 w-4" />}
                    >
                      Delete
                    </Button>
                  </div>
                </div>
              </Card>
            );
          })}
        </div>
      </div>

      <RoleCreateDialog
        open={createDialogOpen}
        onClose={createDialogHandlers.close}
        orgId={orgId || ""}
      />

      {editing && (
        <RoleEditDialog
          open={editDialogOpen}
          onClose={() => {
            editDialogHandlers.close();
            setEditing(null);
          }}
          orgId={orgId || ""}
          initialName={editing.name}
          initialPermission={editing.permission}
          isBuiltInRole={isBuiltIn(editing.name)}
        />
      )}

      <DeleteDialog
        title="Role"
        open={openDeleteDialog}
        onClose={() => {
          deleteDialogHandlers.close();
          setRoleToDelete(null);
        }}
        onDelete={onConfirmDelete}
        loading={deleteRole.isPending}
      />
    </Card>
  );
}
