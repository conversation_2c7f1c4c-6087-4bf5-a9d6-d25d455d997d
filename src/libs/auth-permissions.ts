/**
 * Simplified Better Auth permissions configuration
 *
 * This module defines the access control vocabulary and roles for the organization plugin.
 * It follows Better Auth best practices and uses standard methods.
 */

import { createAccessControl } from "better-auth/plugins/access";
import {
  adminAc,
  defaultStatements,
  memberAc,
} from "better-auth/plugins/organization/access";

/**
 * Define the permission vocabulary for your application.
 * Make sure to use `as const` so TypeScript can infer the type correctly.
 */
const statement = {
  ...defaultStatements, // Include Better Auth's default permissions
  // Your custom application permissions
  project: [
    "create",
    "read",
    "update",
    "delete",
    "submit",
    "approve",
    "publish",
  ],
  approval: ["review"],
  location: ["read", "assign", "manage"],
} as const;

/**
 * Create the access control instance with your permission vocabulary
 */
export const ac = createAccessControl(statement);

/**
 * Define roles using Better Auth's standard approach
 */

// Admin role - has all permissions including Better Auth defaults
export const admin = ac.newRole({
  ...adminAc.statements, // Include all Better Auth admin permissions
  // Add custom application permissions
  project: [
    "create",
    "read",
    "update",
    "delete",
    "submit",
    "approve",
    "publish",
  ],
  approval: ["review"],
  location: ["read", "assign", "manage"],
});

// Manager role - can manage projects and review approvals
export const manager = ac.newRole({
  ...memberAc.statements, // Include Better Auth member permissions
  project: ["create", "read", "update", "submit"],
  approval: ["review"],
  location: ["read", "assign"],
});

// Member role - basic project permissions
export const member = ac.newRole({
  project: ["create", "read", "update", "submit"],
  location: ["read"],
});

/**
 * Helper function to sanitize permission overrides against our vocabulary
 * This ensures only valid permissions are used
 */
export const sanitizeOverrides = (
  input: Record<string, unknown> | undefined | null,
): Record<string, string[]> => {
  if (!input || typeof input !== "object") return {};

  const sanitized: Record<string, string[]> = {};

  for (const [resource, value] of Object.entries(input)) {
    if (!(resource in statement)) continue;

    const actions = Array.isArray(value) ? value : [];
    const validActions = actions.filter(
      (action): action is string =>
        typeof action === "string" &&
        (statement as any)[resource].includes(action),
    );

    if (validActions.length > 0) {
      sanitized[resource] = Array.from(new Set(validActions));
    }
  }

  return sanitized;
};

/**
 * Export the statement for use in other parts of the application
 */
export const getStatements = () => statement;

/**
 * Type definitions for better TypeScript support
 */
export type Resource = keyof typeof statement;
export type Action = (typeof statement)[keyof typeof statement][number];
export type ResourceActions = Record<string, string[]>;
export type CapabilitySpec = Record<string, string[]>;
