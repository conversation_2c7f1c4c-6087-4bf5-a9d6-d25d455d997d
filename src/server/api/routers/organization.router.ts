import { auth } from "@/libs/auth";
import { sanitizeOverrides } from "@/libs/auth-permissions";
import { FILTER_TAKE } from "@/libs/constants";
import { filterSchema } from "@/schemas/api.schemas";
import {
  OrgCreateSchema,
  OrgUpdateSchema,
} from "@/schemas/organization.schemas";
import { Roles } from "@/types/organization.types";
import { Prisma } from "@prisma/client";
import { TRPCError } from "@trpc/server";
import { headers } from "next/headers";
import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "../trpc";

export const organizationsRouter = createTRPCRouter({
  create: protectedProcedure
    .input(OrgCreateSchema)
    .mutation(async ({ ctx, input }) => {
      try {
        const organization = await ctx.db.organization.create({
          data: {
            ...input,
            metadata: { creatorId: ctx.session.user.id },
            members: {
              create: {
                userId: ctx.session.user.id,
                role: Roles.ADMIN,
              },
            },
          },
        });

        return organization;
      } catch (error) {
        if (error instanceof Prisma.PrismaClientKnownRequestError) {
          if (
            error.code === "P2002" &&
            error.meta?.target === "organizations_slug_key"
          ) {
            throw new TRPCError({
              code: "CONFLICT",
              message: "The organization slug is already in use.",
            });
          }
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: error.message,
          });
        }
      }
    }),
  getAll: protectedProcedure
    .input(z.object({ ...filterSchema }))
    .query(async ({ ctx, input }) => {
      const userId = ctx.session.user.id;

      const take = input?.take || FILTER_TAKE;

      const data = await ctx.db.organization.findMany({
        where: {
          name: {
            contains: input?.searchString,
          },
          members: {
            some: {
              userId,
              deletedAt: null,
            },
          },
          deletedAt: null,
        },
        include: {
          _count: {
            select: {
              members: true,
            },
          },
        },
        ...(input.cursor && {
          cursor: {
            id: input.cursor,
          },
          skip: 1,
        }),
        take,
        orderBy: { createdAt: "desc" },
      });

      const result = { data, cursor: "" };

      if (data.length < take) return result;

      return { ...result, cursor: data.at(-1)?.id || "" };
    }),
  getById: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      const org = await ctx.db.organization.findUnique({
        where: { id: input.id, deletedAt: null },
        include: {
          _count: {
            select: {
              members: true,
            },
          },
        },
      });

      if (org) {
        // Check if user is a member of the organization
        const isMember = await ctx.db.member.findUnique({
          where: {
            member_userId_organizationId_key: {
              userId: ctx.session.user.id,
              organizationId: org.id,
            },
            deletedAt: null,
          },
        });

        if (!isMember) {
          // User is not a member, check for pending invites
          const pendingInvite = await ctx.db.invitation.findUnique({
            where: {
              email_organizationId: {
                email: ctx.session.user.email,
                organizationId: org.id,
              },
              deletedAt: null,
            },
            select: {
              expiresAt: true,
            },
          });

          if (!pendingInvite) {
            throw new TRPCError({
              code: "FORBIDDEN",
              message: "You do not have access to this organization.",
            });
          } else if (pendingInvite.expiresAt < new Date()) {
            throw new TRPCError({
              code: "CLIENT_CLOSED_REQUEST",
              message: "Organization invite expired",
            });
          } else {
            throw new TRPCError({
              code: "CONFLICT",
              message: `${org.name}`,
            });
          }
        }
      } else {
        // Org doesn't exist
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Organization not found",
        });
      }

      return { ...org };
    }),
  getBySlug: protectedProcedure
    .input(z.object({ slug: z.string() }))
    .query(async ({ ctx, input }) => {
      const org = await ctx.db.organization.findUnique({
        where: { slug: input.slug, deletedAt: null },
        include: {
          _count: {
            select: {
              members: true,
            },
          },
        },
      });

      if (org) {
        // Check if user is a member of the organization
        const isMember = await ctx.db.member.findUnique({
          where: {
            member_userId_organizationId_key: {
              userId: ctx.session.user.id,
              organizationId: org.id,
            },
            deletedAt: null,
          },
        });

        if (!isMember) {
          // User is not a member, check for pending invites
          const pendingInvite = await ctx.db.invitation.findUnique({
            where: {
              email_organizationId: {
                email: ctx.session.user.email,
                organizationId: org.id,
              },
              deletedAt: null,
            },
            select: {
              expiresAt: true,
            },
          });

          if (!pendingInvite) {
            throw new TRPCError({
              code: "FORBIDDEN",
              message: "You do not have access to this organization.",
            });
          } else if (pendingInvite.expiresAt < new Date()) {
            throw new TRPCError({
              code: "CLIENT_CLOSED_REQUEST",
              message: "Organization invite expired",
            });
          } else {
            throw new TRPCError({
              code: "CONFLICT",
              message: `${org.name}`,
            });
          }
        }
      } else {
        // Org doesn't exist
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Organization not found",
        });
      }

      return { ...org };
    }),
  updateById: protectedProcedure
    .input(z.object({ id: z.string() }).merge(OrgUpdateSchema))
    .mutation(async ({ ctx, input }) => {
      return await ctx.db.organization.update({
        where: { id: input.id },
        data: { name: input.name, logo: input.logo, slug: input.slug },
      });
    }),
  deleteById: protectedProcedure
    .input(
      z.object({ id: z.string(), stripeCustomerId: z.string().optional() }),
    )
    .mutation(async ({ ctx, input }) => {
      return await ctx.db.organization.update({
        where: { id: input.id },
        data: { deletedAt: new Date(), slug: null },
      });
    }),
  getMembers: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      return await ctx.db.member.findMany({
        where: { organizationId: input.id, deletedAt: null },
        select: {
          id: true,
          role: true,
          organizationId: true,
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              image: true,
            },
          },
          organization: {
            select: {
              id: true,
              name: true,
            },
          },
          createdAt: true,
        },
        orderBy: { createdAt: "asc" },
      });
    }),
  getMemberRole: protectedProcedure
    .input(z.object({ id: z.string(), orgId: z.string() }))
    .query(async ({ ctx, input }) => {
      const member = await ctx.db.member.findUnique({
        where: {
          member_userId_organizationId_key: {
            userId: input.id,
            organizationId: input.orgId,
          },
          deletedAt: null,
        },
        select: {
          role: true,
        },
      });

      if (!member) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Member not found",
        });
      }

      return member;
    }),
  updateMemberRole: protectedProcedure
    .input(
      z.object({
        memberId: z.string(),
        role: z.string().min(1),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      return await ctx.db.member.update({
        where: { id: input.memberId },
        data: { role: input.role },
      });
    }),
  deleteMember: protectedProcedure
    .input(z.object({ memberId: z.string() }))
    .mutation(async ({ ctx, input }) => {
      return await ctx.db.member.update({
        where: { id: input.memberId },
        data: { deletedAt: new Date() },
      });
    }),
  getInvites: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      return await ctx.db.invitation.findMany({
        where: { organizationId: input.id, status: "pending", deletedAt: null },
        orderBy: { createdAt: "desc" },
      });
    }),
  getInvite: protectedProcedure
    .input(z.object({ orgId: z.string(), email: z.string() }))
    .query(async ({ ctx, input }) => {
      const invite = await ctx.db.invitation.findFirst({
        where: {
          email: input?.email as string,
          organizationId: input.orgId as string,
          deletedAt: null,
        },
        select: {
          expiresAt: true,
          organizationId: true,
          organization: {
            select: {
              name: true,
            },
          },
        },
      });

      if (!invite) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Invalid invitation",
        });
      }

      if (invite.expiresAt < new Date()) {
        throw new TRPCError({
          code: "CONFLICT",
          message: "Invitation expired",
        });
      }

      return invite;
    }),
  acceptInvite: protectedProcedure
    .input(z.object({ orgId: z.string(), email: z.string().optional() }))
    .mutation(async ({ ctx, input }) => {
      const invite = await ctx.db.invitation.findFirst({
        where: {
          email: input?.email as string,
          organizationId: input.orgId,
          deletedAt: null,
        },
        select: {
          expiresAt: true,
          organizationId: true,
        },
      });

      if (!invite) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Invalid invitation",
        });
      } else if (invite.expiresAt < new Date()) {
        throw new TRPCError({
          code: "CONFLICT",
          message: "Invitation expired",
        });
      } else {
        const response = await Promise.all([
          ctx.db.member.create({
            data: {
              userId: ctx.session.user.id,
              role: Roles.MANAGER,
              organizationId: invite.organizationId,
            },
          }),
          ctx.db.invitation.delete({
            where: {
              email_organizationId: {
                email: ctx.session.user.email as string,
                organizationId: invite.organizationId,
              },
            },
          }),
        ]);
        return response;
      }
    }),
  deleteInvite: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      return await ctx.db.invitation.update({
        where: { id: input.id },
        data: { deletedAt: new Date() },
      });
    }),
  /**
   * Add / merge additive permission overrides for a role within an organization.
   * Admin only. Overrides are additive; invalid resources/actions are ignored.
   * Input example:
   *   { orgId, role: "manager", permissions: { project: ["publish"], location: ["assign"] } }
   */
  updatePermissionOverrides: protectedProcedure
    .input(
      z.object({
        orgId: z.string(),
        role: z.string().min(1),
        permissions: z.record(z.string(), z.array(z.string()).nonempty()),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      // Ensure caller is admin of this org
      const adminMember = await ctx.db.member.findUnique({
        where: {
          member_userId_organizationId_key: {
            userId: ctx.session.user.id,
            organizationId: input.orgId,
          },
          deletedAt: null,
        },
        select: { role: true },
      });

      if (!adminMember || adminMember.role !== Roles.ADMIN) {
        throw new TRPCError({ code: "FORBIDDEN", message: "Not authorized" });
      }

      // Sanitize incoming permissions against AC vocabulary
      const incoming = sanitizeOverrides(input.permissions);
      if (Object.keys(incoming).length === 0) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "No valid permissions supplied",
        });
      }

      // Load current role permission from Better Auth and merge additively
      const role = await auth.api.getOrgRole({
        query: { organizationId: input.orgId, roleName: input.role },
        headers: ctx.headers,
      });
      const current = (role.permission || {}) as Record<string, string[]>;

      const merged: Record<string, string[]> = {};
      const resources = new Set([
        ...Object.keys(current),
        ...Object.keys(incoming),
      ]);
      for (const r of resources) {
        merged[r] = Array.from(
          new Set([...(current[r] || []), ...(incoming[r] || [])]),
        );
      }

      const updated = await auth.api.updateOrgRole({
        body: {
          organizationId: input.orgId,
          roleName: input.role,
          data: { permission: merged },
        },
        headers: ctx.headers,
      });

      const permission = updated.roleData?.permission ?? merged;
      return { role: input.role, permission };
    }),
  upsertCustomRoleBase: protectedProcedure
    .input(
      z.object({
        orgId: z.string(),
        role: z.string().min(1),
        permissions: z.record(z.string(), z.array(z.string()).nonempty()),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      // Ensure caller is admin of this org
      const adminMember = await ctx.db.member.findUnique({
        where: {
          member_userId_organizationId_key: {
            userId: ctx.session.user.id,
            organizationId: input.orgId,
          },
          deletedAt: null,
        },
        select: { role: true },
      });

      if (!adminMember || adminMember.role !== Roles.ADMIN) {
        throw new TRPCError({ code: "FORBIDDEN", message: "Not authorized" });
      }

      // Sanitize base permissions against AC vocabulary
      const base = sanitizeOverrides(input.permissions);
      if (Object.keys(base).length === 0) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "No valid permissions supplied",
        });
      }

      // If role exists, update; otherwise create
      try {
        await auth.api.getOrgRole({
          query: { organizationId: input.orgId, roleName: input.role },
          headers: ctx.headers,
        });
        const updated = await auth.api.updateOrgRole({
          body: {
            organizationId: input.orgId,
            roleName: input.role,
            data: { permission: base },
          },
          headers: ctx.headers,
        });
        return updated.roleData ?? updated;
      } catch {
        const created = await auth.api.createOrgRole({
          body: {
            role: input.role,
            permission: base,
            organizationId: input.orgId,
          },
          headers: ctx.headers,
        });
        return created;
      }
    }),

  // ----- Better Auth Role CRUD (thin wrappers) -----
  listRoles: protectedProcedure
    .input(z.object({ orgId: z.string() }))
    .query(async ({ ctx, input }) => {
      // Any member can list roles
      const member = await ctx.db.member.findUnique({
        where: {
          member_userId_organizationId_key: {
            userId: ctx.session.user.id,
            organizationId: input.orgId,
          },
          deletedAt: null,
        },
        select: { id: true, role: true },
      });
      if (!member) {
        throw new TRPCError({ code: "FORBIDDEN", message: "Not a member" });
      }

      // Debug logging
      console.log("🔍 listRoles Debug Info:");
      console.log("  User ID:", ctx.session.user.id);
      console.log("  Organization ID:", input.orgId);
      console.log("  Member role from DB:", member.role);
      console.log("  Headers keys:", Object.keys(ctx.headers));

      try {
        const roles = await auth.api.listOrgRoles({
          query: { organizationId: input.orgId },
          headers: await headers(),
        });
        console.log("✅ listOrgRoles succeeded");
        return roles;
      } catch (error) {
        console.log("❌ listOrgRoles failed:", error);
        throw error;
      }
    }),
  getRole: protectedProcedure
    .input(
      z.object({
        orgId: z.string(),
        roleName: z.string(),
      }),
    )
    .query(async ({ ctx, input }) => {
      const member = await ctx.db.member.findUnique({
        where: {
          member_userId_organizationId_key: {
            userId: ctx.session.user.id,
            organizationId: input.orgId,
          },
          deletedAt: null,
        },
        select: { id: true },
      });
      if (!member) {
        throw new TRPCError({ code: "FORBIDDEN", message: "Not a member" });
      }
      const role = await auth.api.getOrgRole({
        query: { organizationId: input.orgId, roleName: input.roleName },
        headers: ctx.headers,
      });
      return role;
    }),
  createRole: protectedProcedure
    .input(
      z.object({
        orgId: z.string(),
        role: z.string().min(1),
        permission: z.record(z.string(), z.array(z.string()).nonempty()),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      // Admin only
      const admin = await ctx.db.member.findUnique({
        where: {
          member_userId_organizationId_key: {
            userId: ctx.session.user.id,
            organizationId: input.orgId,
          },
          deletedAt: null,
        },
        select: { role: true },
      });
      if (!admin || admin.role !== Roles.ADMIN) {
        throw new TRPCError({ code: "FORBIDDEN", message: "Not authorized" });
      }
      const perm = sanitizeOverrides(input.permission);
      if (Object.keys(perm).length === 0) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "No valid permissions supplied",
        });
      }
      try {
        console.log("Creating role with permissions:", {
          organizationId: input.orgId,
          role: input.role,
          permission: perm,
          userId: ctx.session.user.id,
          userRole: admin.role,
        });

        const created = await auth.api.createOrgRole({
          body: {
            organizationId: input.orgId,
            role: input.role,
            permission: perm,
          },
          headers: ctx.headers,
        });

        console.log("Role created successfully:", created);
        return created.roleData;
      } catch (error) {
        console.error("Better Auth role creation failed:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: `Failed to create role: ${error instanceof Error ? error.message : "Unknown error"}`,
        });
      }
    }),
  updateRole: protectedProcedure
    .input(
      z.object({
        orgId: z.string(),
        roleName: z.string(),
        data: z.object({
          permission: z
            .record(z.string(), z.array(z.string()).nonempty())
            .optional(),
          roleName: z.string().min(1).optional(),
        }),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const admin = await ctx.db.member.findUnique({
        where: {
          member_userId_organizationId_key: {
            userId: ctx.session.user.id,
            organizationId: input.orgId,
          },
          deletedAt: null,
        },
        select: { role: true },
      });
      if (!admin || admin.role !== Roles.ADMIN) {
        throw new TRPCError({ code: "FORBIDDEN", message: "Not authorized" });
      }
      const sanitized = input.data.permission
        ? sanitizeOverrides(input.data.permission)
        : undefined;
      const updated = await auth.api.updateOrgRole({
        body: {
          organizationId: input.orgId,
          roleName: input.roleName,
          data: {
            ...(sanitized ? { permission: sanitized } : {}),
            ...(input.data.roleName ? { roleName: input.data.roleName } : {}),
          },
        },
        headers: ctx.headers,
      });
      return updated.roleData;
    }),
  deleteRole: protectedProcedure
    .input(
      z.object({
        orgId: z.string(),
        roleName: z.string(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const admin = await ctx.db.member.findUnique({
        where: {
          member_userId_organizationId_key: {
            userId: ctx.session.user.id,
            organizationId: input.orgId,
          },
          deletedAt: null,
        },
        select: { role: true },
      });
      if (!admin || admin.role !== Roles.ADMIN) {
        throw new TRPCError({ code: "FORBIDDEN", message: "Not authorized" });
      }
      const res = await auth.api.deleteOrgRole({
        body: {
          organizationId: input.orgId,
          roleName: input.roleName,
        },
        headers: ctx.headers,
      });
      return res;
    }),
});
